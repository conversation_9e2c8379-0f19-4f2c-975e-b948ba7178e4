//
//  LNAnchorListViewModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/22.
//

import Foundation

// MARK: - ViewModel 协议
protocol LNAnchorListViewModelDelegate: AnyObject {
    /// 数据加载完成
    func anchorListDidLoad(_ viewModel: LNAnchorListViewModel, type: LNAnchorListType)
    /// 数据加载失败
    func anchorListDidFail(_ viewModel: LNAnchorListViewModel, type: LNAnchorListType, error: Error)
    /// 开始加载
    func anchorListDidStartLoading(_ viewModel: LNAnchorListViewModel, type: LNAnchorListType)
}

// MARK: - 主播列表 ViewModel
/// 主播列表 ViewModel - 统一管理 Hot、New、Follow 三个页面的数据
class LNAnchorListViewModel {
    
    // MARK: - Properties
    weak var delegate: LNAnchorListViewModelDelegate?
    
    /// 每页数据量
    private let pageSize: Int = 20
    
    /// 各类型的数据存储
    private var hotAnchors: [LNAnchorModel] = []
    private var newAnchors: [LNAnchorModel] = []
    private var followAnchors: [LNAnchorModel] = []
    
    /// 各类型的当前页码
    private var hotCurrentPage: Int = 1
    private var newCurrentPage: Int = 1
    private var followCurrentPage: Int = 1
    
    /// 各类型的加载状态
    private var hotLoading: Bool = false
    private var newLoading: Bool = false
    private var followLoading: Bool = false
    
    /// 各类型是否还有更多数据
    private var hotHasMore: Bool = true
    private var newHasMore: Bool = true
    private var followHasMore: Bool = true
    
    // MARK: - Public Methods
    
    /// 获取指定类型的主播列表
    func getAnchors(for type: LNAnchorListType) -> [LNAnchorModel] {
        switch type {
        case .hot:
            return hotAnchors
        case .new:
            return newAnchors
        case .follow:
            return followAnchors
        }
    }
    
    /// 获取指定类型的 LNLiveStreamModel 列表（兼容现有代码）
    func getLiveStreams(for type: LNAnchorListType) -> [LNLiveStreamModel] {
        return getAnchors(for: type).map { $0.toLiveStreamModel() }
    }
    
    /// 是否正在加载
    func isLoading(for type: LNAnchorListType) -> Bool {
        switch type {
        case .hot:
            return hotLoading
        case .new:
            return newLoading
        case .follow:
            return followLoading
        }
    }
    
    /// 是否还有更多数据
    func hasMore(for type: LNAnchorListType) -> Bool {
        switch type {
        case .hot:
            return hotHasMore
        case .new:
            return newHasMore
        case .follow:
            return followHasMore
        }
    }
    
    /// 加载数据（刷新）
    func loadData(for type: LNAnchorListType, refresh: Bool = true) {
        if isLoading(for: type) {
            return
        }
        
        if refresh {
            resetPagination(for: type)
        }
        
        let currentPage = getCurrentPage(for: type)
        setLoading(true, for: type)
        delegate?.anchorListDidStartLoading(self, type: type)
        
        _getAnchorList(type: type, current: currentPage)
    }
    
    /// 加载更多数据
    func loadMore(for type: LNAnchorListType) {
        if isLoading(for: type) || !hasMore(for: type) {
            return
        }
        
        incrementPage(for: type)
        let currentPage = getCurrentPage(for: type)
        setLoading(true, for: type)
        
        _getAnchorList(type: type, current: currentPage)
    }
    
    // MARK: - Private Methods
    
    /// 核心数据请求方法（对应 Flutter 的 _getAnchorList 方法）
    private func _getAnchorList(type: LNAnchorListType, current: Int) {
        // 构建请求参数
        let params = [
            "current": "\(current)",
            "size": "\(pageSize)"
        ]
        
        // 根据类型选择 API
        let apiTarget: LNApiAnchor
        switch type {
        case .hot:
            apiTarget = .popularList(par: params)
        case .new:
            apiTarget = .newList(par: params)
        case .follow:
            apiTarget = .followList(par: params)
        }
        
        // 发起网络请求
        NetWorkRequest(apiTarget, completion: { [weak self] result in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.setLoading(false, for: type)
                self.handleSuccess(result: result, type: type, current: current)
            }
            
        }, failure: { [weak self] error in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.setLoading(false, for: type)
                self.handleFailure(error: error, type: type, current: current)
            }
        })
    }
    
    /// 处理成功响应
    private func handleSuccess(result: [String: Any], type: LNAnchorListType, current: Int) {
        // 解析响应数据
        if let dataDict = result["data"] as? [String: Any],
           let jsonData = try? JSONSerialization.data(withJSONObject: dataDict),
           let pageResponse = LNPageResponse<LNAnchorModel>.deserialize(from: String(data: jsonData, encoding: .utf8)) {
            
            let newItems = pageResponse.records
            
            // 更新数据
            if current == 1 {
                // 刷新数据
                setAnchors(newItems, for: type)
            } else {
                // 追加数据
                appendAnchors(newItems, for: type)
            }
            
            // 更新分页状态
            let hasMore = pageResponse.current < pageResponse.pages
            setHasMore(hasMore, for: type)
            
            // 通知代理
            delegate?.anchorListDidLoad(self, type: type)
            
        } else {
            // 数据解析失败
            let error = NSError(domain: "LNAnchorListViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
            handleFailure(error: error, type: type, current: current)
        }
    }
    
    /// 处理失败响应
    private func handleFailure(error: Error, type: LNAnchorListType, current: Int) {
        // 如果不是第一页，需要回退页码
        if current > 1 {
            decrementPage(for: type)
        }
        
        // 通知代理
        delegate?.anchorListDidFail(self, type: type, error: error)
    }
    
    // MARK: - Helper Methods
    
    private func setAnchors(_ anchors: [LNAnchorModel], for type: LNAnchorListType) {
        switch type {
        case .hot:
            hotAnchors = anchors
        case .new:
            newAnchors = anchors
        case .follow:
            followAnchors = anchors
        }
    }
    
    private func appendAnchors(_ anchors: [LNAnchorModel], for type: LNAnchorListType) {
        switch type {
        case .hot:
            hotAnchors.append(contentsOf: anchors)
        case .new:
            newAnchors.append(contentsOf: anchors)
        case .follow:
            followAnchors.append(contentsOf: anchors)
        }
    }
    
    private func getCurrentPage(for type: LNAnchorListType) -> Int {
        switch type {
        case .hot:
            return hotCurrentPage
        case .new:
            return newCurrentPage
        case .follow:
            return followCurrentPage
        }
    }
    
    private func incrementPage(for type: LNAnchorListType) {
        switch type {
        case .hot:
            hotCurrentPage += 1
        case .new:
            newCurrentPage += 1
        case .follow:
            followCurrentPage += 1
        }
    }
    
    private func decrementPage(for type: LNAnchorListType) {
        switch type {
        case .hot:
            hotCurrentPage = max(1, hotCurrentPage - 1)
        case .new:
            newCurrentPage = max(1, newCurrentPage - 1)
        case .follow:
            followCurrentPage = max(1, followCurrentPage - 1)
        }
    }
    
    private func resetPagination(for type: LNAnchorListType) {
        switch type {
        case .hot:
            hotCurrentPage = 1
            hotHasMore = true
        case .new:
            newCurrentPage = 1
            newHasMore = true
        case .follow:
            followCurrentPage = 1
            followHasMore = true
        }
    }
    
    private func setLoading(_ loading: Bool, for type: LNAnchorListType) {
        switch type {
        case .hot:
            hotLoading = loading
        case .new:
            newLoading = loading
        case .follow:
            followLoading = loading
        }
    }
    
    private func setHasMore(_ hasMore: Bool, for type: LNAnchorListType) {
        switch type {
        case .hot:
            hotHasMore = hasMore
        case .new:
            newHasMore = hasMore
        case .follow:
            followHasMore = hasMore
        }
    }
}
