//
//  LNAnchorModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/22.
//

import Foundation
import UIKit
import HandyJSON

// MARK: - 主播数据模型
/// 主播数据模型 - 对应 API 返回的用户数据
class LNAnchorModel: BaseModel {
    var age: Int = 0
    var agoraRtcAppId: String = ""
    var anchorFileList: [String] = []
    var anchorLabelList: [String] = []
    var auditFlag: String = ""
    var birthday: String = ""
    var constellation: String = ""
    var country: String = ""
    var coverVideoUrl: String = ""
    var currentGradeStd: Int = 0
    var dayFreeFreshTime: String = ""
    var dayFreeFreshTimeStamp: String = ""
    var diamond: Int = 0
    var diamondTotal: String = ""
    var email: String = ""
    var facebookClientToken: String = ""
    var facebookId: String = ""
    var fansNum: Int = 0
    var firstFlag: Int = 1
    var firstRecharge: Int = 1
    var firstRechargeExpireTime: String = ""
    var followNum: Int = 0
    var followFlag: String = ""
    var freeRandomMatch: Int = 0
    var freeVideoCall: Int = 0
    var gender: Int = 0
    var giveDiamond: Int = 0
    var hasGoogleRelation: String = ""
    var hasThirdPay: String = ""
    var headFileName: String = ""
    var height: String = ""
    var id: Int = 0
    var incomeDiamond: Int = 0
    var incomeDiamondDecimal: String = ""
    var initialFreeRandomMatch: Int = 2
    var initialFreeVideoCall: Int = 0
    var initialFreeVideoPlay: String = ""
    var languages: [String] = []
    var lastIp: String = ""
    var level: String = ""
    var levelKey: Int = 0
    var msgSendNum: Int = 0
    var nextGradeDiff: Int = 0
    var nextGradeStd: Int = 0
    var nickName: String = ""
    var onlineStatus: String = ""
    var payInfoStatus: String = ""
    var phone: String = ""
    var photos: [String] = []
    var power: String = ""
    var remark: String = ""
    var rongCloudAppKey: String = ""
    var showVideoUrl: String = ""
    var singleRecharge: Int = 0
    var status: String = ""
    var signature: String = ""
    var token: String = ""
    var trends: [Any] = []
    var trendsNum: String = ""
    var unionId: Int = 0
    var userCategory: String = ""
    var userCode: String = ""
    var userRole: Int = 0
    var username: String = ""
    var videoPrice: Int = 0
    var vipExpireDay: String = ""
    var vipExpireFlag: Int = 0
    var vipVideoPrice: Int = 0
    var vipFlag: String = ""
    var weight: String = ""
    
    // MARK: - 计算属性
    /// 显示名称
    var displayName: String {
        return nickName.isEmpty ? "NIKENAME" : nickName
    }
    
    /// 用户状态
    var userStatus: LNUserStatus {
        switch onlineStatus {
        case "1":
            return .online
        case "2":
            return .busy
        default:
            return .offline
        }
    }
    
    /// 国家旗帜（根据country字段映射）
    var countryFlag: String {
        switch country.lowercased() {
        case "palestine", "ps":
            return "🇵🇸"
        case "jordan", "jo":
            return "🇯🇴"
        case "indonesia", "id":
            return "🇮🇩"
        case "philippines", "ph":
            return "🇵🇭"
        case "vietnam", "vn":
            return "🇻🇳"
        case "thailand", "th":
            return "🇹🇭"
        default:
            return "🇺🇸"
        }
    }
    
    /// 每分钟价格显示
    var pricePerMinuteDisplay: String {
        if videoPrice > 0 {
            return "\(videoPrice)/min"
        }
        return "60/min"
    }
    
    /// 头像图片名称（如果没有网络头像，使用默认）
    var avatarImageName: String {
        if !headFileName.isEmpty {
            return headFileName
        }
        return "avatar\(id % 6 + 1)" // 默认头像
    }
    
    /// 是否支持视频通话
    var hasVideoCall: Bool {
        return freeVideoCall > 0 || videoPrice > 0
    }
    
    /// 是否有礼物
    var hasGift: Bool {
        return giveDiamond > 0
    }
    
    /// 转换为 LNLiveStreamModel（兼容现有代码）
    func toLiveStreamModel() -> LNLiveStreamModel {
        return LNLiveStreamModel(
            id: "\(id)",
            username: displayName,
            status: userStatus,
            countryFlag: countryFlag,
            pricePerMinute: pricePerMinuteDisplay,
            avatarImageName: avatarImageName,
            hasVideoCall: hasVideoCall,
            hasGift: hasGift
        )
    }
}

// MARK: - 主播列表API响应模型
/// 主播列表API响应模型
class LNAnchorListResponse: BaseModel {
    var code: Int = 0
    var data: LNPageResponse<LNAnchorModel>?
    var msg: String = ""
    var success: Bool = false
}

// MARK: - 主播列表类型枚举
/// 主播列表类型
enum LNAnchorListType: Int, CaseIterable {
    case hot = 0        // 热门
    case new = 1        // 最新
    case follow = 2     // 关注
    
    var title: String {
        switch self {
        case .hot:
            return "Hot"
        case .new:
            return "New"
        case .follow:
            return "Follow"
        }
    }
    
    var apiCase: LNApiAnchor {
        switch self {
        case .hot:
            return .popularList(par: [:])
        case .new:
            return .newList(par: [:])
        case .follow:
            return .followList(par: [:])
        }
    }
}
