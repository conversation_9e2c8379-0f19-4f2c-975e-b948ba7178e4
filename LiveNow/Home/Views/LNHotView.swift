//
//  LNHotView.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit
import SnapKit
import JKSwiftExtension
import JXSegmentedView
import MJRefresh

/// Hot 页面视图 - 展示热门直播内容
class LNHotView: UIView {

    // MARK: - UI Elements
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = s(15)
        layout.sectionInset = UIEdgeInsets(top: s(10), left: s(15), bottom: s(10), right: s(15))

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNLiveStreamCell.self, forCellWithReuseIdentifier: "LNLiveStreamCell")
        return collectionView
    }()

    // MARK: - Data
    private var liveStreams: [LNLiveStreamModel] = []
    private var currentPage: Int = 1
    private var hasMore: Bool = true
    private var isLoading: Bool = false
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupRefresh()
        loadData()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Private Methods
    private func setupUI() {
        backgroundColor = .clear
        addSubview(collectionView)
    }

    private func setupConstraints() {
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupRefresh() {
        // 下拉刷新
        collectionView.mj_header = MJRefreshNormalHeader { [weak self] in
            self?.loadData(refresh: true)
        }

        // 上拉加载更多
        collectionView.mj_footer = MJRefreshAutoNormalFooter { [weak self] in
            self?.loadMore()
        }
    }

    private func loadData(refresh: Bool = true) {
        if isLoading {
            return
        }

        if refresh {
            currentPage = 1
            hasMore = true
            collectionView.mj_footer?.resetNoMoreData()
        }

        // 使用共享方法加载数据
        LNHotView.loadAnchorData(
            apiTarget: .popularList(par: ["current": "\(currentPage)", "size": "20"]),
            current: currentPage,
            completion: { [weak self] newLiveStreams, hasMoreData in
                guard let self = self else { return }

                DispatchQueue.main.async {
                    self.isLoading = false

                    // 更新数据
                    if self.currentPage == 1 {
                        // 刷新数据
                        self.liveStreams = newLiveStreams
                    } else {
                        // 追加数据
                        self.liveStreams.append(contentsOf: newLiveStreams)
                    }

                    // 更新分页状态
                    self.hasMore = hasMoreData

                    // 刷新UI
                    self.collectionView.reloadData()

                    // 结束刷新状态
                    self.collectionView.mj_header?.endRefreshing()

                    // 处理加载更多状态
                    if self.hasMore {
                        self.collectionView.mj_footer?.endRefreshing()
                    } else {
                        self.collectionView.mj_footer?.endRefreshingWithNoMoreData()
                    }
                }
            },
            failure: { [weak self] error in
                guard let self = self else { return }

                DispatchQueue.main.async {
                    self.isLoading = false

                    // 如果不是第一页，需要回退页码
                    if self.currentPage > 1 {
                        self.currentPage = max(1, self.currentPage - 1)
                    }

                    // 结束刷新状态
                    self.collectionView.mj_header?.endRefreshing()
                    self.collectionView.mj_footer?.endRefreshing()

                    // 显示错误信息
                    print("加载热门主播失败: \(error.localizedDescription)")
                    // TODO: 显示错误提示
                }
            }
        )
    }

    private func loadMore() {
        if isLoading || !hasMore {
            return
        }

        currentPage += 1
        loadData(refresh: false)
    }


}

// MARK: - UICollectionViewDataSource
extension LNHotView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return liveStreams.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNLiveStreamCell", for: indexPath) as! LNLiveStreamCell
        cell.configure(with: liveStreams[indexPath.item])
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension LNHotView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (kscreenW - s(45)) / 2
        let height = s(280)
        return CGSize(width: width, height: height)
    }
}

// MARK: - UICollectionViewDelegate
extension LNHotView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let liveStream = liveStreams[indexPath.item]
        print("选中了直播间: \(liveStream.username)")
        // TODO: 跳转到直播间详情页
        let alert = LNVideoCallModal()
        alert.show(with: LNUserModel())
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension LNHotView: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return self
    }
}
