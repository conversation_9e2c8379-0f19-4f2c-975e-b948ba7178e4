//
//  LNNewView.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit
import SnapKit
import JKSwiftExtension
import JXSegmentedView
import MJRefresh

/// New 页面视图 - 展示最新直播内容
class LNNewView: UIView {

    // MARK: - UI Elements
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = s(15)
        layout.sectionInset = UIEdgeInsets(top: s(10), left: s(15), bottom: s(10), right: s(15))


        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNLiveStreamCell.self, forCellWithReuseIdentifier: "LNLiveStreamCell")
        return collectionView
    }()

    // MARK: - Data
    private var liveStreams: [LNLiveStreamModel] = []
    private var currentPage: Int = 1
    private let pageSize: Int = 20
    private var hasMore: Bool = true
    private var isLoading: Bool = false
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupRefresh()
        loadData()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Private Methods
    private func setupUI() {
        backgroundColor = .clear
        addSubview(collectionView)
    }

    private func setupConstraints() {
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupRefresh() {
        // 下拉刷新
        collectionView.mj_header = MJRefreshNormalHeader { [weak self] in
            self?.loadData(refresh: true)
        }

        // 上拉加载更多
        collectionView.mj_footer = MJRefreshAutoNormalFooter { [weak self] in
            self?.loadMore()
        }
    }

    private func loadData(refresh: Bool = true) {
        if isLoading {
            return
        }

        if refresh {
            currentPage = 1
            hasMore = true
            collectionView.mj_footer?.resetNoMoreData()
        }

        _getAnchorList(current: currentPage)
    }

    private func loadMore() {
        if isLoading || !hasMore {
            return
        }

        currentPage += 1
        _getAnchorList(current: currentPage)
    }

    /// 核心数据请求方法（对应 Flutter 的 _getAnchorList 方法）
    private func _getAnchorList(current: Int) {
        isLoading = true

        // 构建请求参数
        let params = [
            "current": "\(current)",
            "size": "\(pageSize)"
        ]

        // 调用最新主播列表接口
        NetWorkRequest(LNApiAnchor.newList(par: params), completion: { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoading = false
                self.handleSuccess(result: result, current: current)
            }

        }, failure: { [weak self] error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoading = false
                self.handleFailure(error: error, current: current)
            }
        })
    }

    /// 处理成功响应
    private func handleSuccess(result: [String: Any], current: Int) {
        // 解析响应数据
        if let dataDict = result["data"] as? [String: Any],
           let jsonData = try? JSONSerialization.data(withJSONObject: dataDict),
           let pageResponse = LNPageResponse<LNUserModel>.deserialize(from: String(data: jsonData, encoding: .utf8)) {

            let newItems = pageResponse.records

            // 转换为 LNLiveStreamModel
            let newLiveStreams = newItems.map { userModel in
                convertUserToLiveStream(userModel)
            }

            // 更新数据
            if current == 1 {
                // 刷新数据
                liveStreams = newLiveStreams
            } else {
                // 追加数据
                liveStreams.append(contentsOf: newLiveStreams)
            }

            // 更新分页状态
            hasMore = pageResponse.current < pageResponse.pages

            // 刷新UI
            collectionView.reloadData()

            // 结束刷新状态
            collectionView.mj_header?.endRefreshing()

            // 处理加载更多状态
            if hasMore {
                collectionView.mj_footer?.endRefreshing()
            } else {
                collectionView.mj_footer?.endRefreshingWithNoMoreData()
            }

        } else {
            // 数据解析失败
            let error = NSError(domain: "LNNewView", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
            handleFailure(error: error, current: current)
        }
    }

    /// 处理失败响应
    private func handleFailure(error: Error, current: Int) {
        // 如果不是第一页，需要回退页码
        if current > 1 {
            currentPage = max(1, currentPage - 1)
        }

        // 结束刷新状态
        collectionView.mj_header?.endRefreshing()
        collectionView.mj_footer?.endRefreshing()

        // 显示错误信息
        print("加载最新主播失败: \(error.localizedDescription)")
        // TODO: 显示错误提示
    }

    /// 将 LNUserModel 转换为 LNLiveStreamModel
    private func convertUserToLiveStream(_ userModel: LNUserModel) -> LNLiveStreamModel {
        let status: LNUserStatus
        switch userModel.onlineStatus {
        case "1":
            status = .online
        case "2":
            status = .busy
        default:
            status = .offline
        }

        let countryFlag: String
        switch userModel.country.lowercased() {
        case "palestine", "ps":
            countryFlag = "🇵🇸"
        case "jordan", "jo":
            countryFlag = "🇯🇴"
        case "indonesia", "id":
            countryFlag = "🇮🇩"
        case "philippines", "ph":
            countryFlag = "🇵🇭"
        case "vietnam", "vn":
            countryFlag = "🇻🇳"
        case "thailand", "th":
            countryFlag = "🇹🇭"
        default:
            countryFlag = "🇺🇸"
        }

        return LNLiveStreamModel(
            id: "\(userModel.id)",
            username: userModel.nickName.isEmpty ? "NIKENAME" : userModel.nickName,
            status: status,
            countryFlag: countryFlag,
            pricePerMinute: userModel.videoPrice > 0 ? "\(userModel.videoPrice)/min" : "60/min",
            avatarImageName: userModel.headFileName.isEmpty ? "avatar\(userModel.id % 6 + 1)" : userModel.headFileName,
            hasVideoCall: userModel.freeVideoCall > 0 || userModel.videoPrice > 0,
            hasGift: userModel.giveDiamond > 0
        )
    }
}

// MARK: - UICollectionViewDataSource
extension LNNewView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return liveStreams.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNLiveStreamCell", for: indexPath) as! LNLiveStreamCell
        cell.configure(with: liveStreams[indexPath.item])
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension LNNewView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (kscreenW - s(45)) / 2
        let height = s(280)
        return CGSize(width: width, height: height)
    }
}

// MARK: - UICollectionViewDelegate
extension LNNewView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let liveStream = liveStreams[indexPath.item]
        print("选中了直播间: \(liveStream.username)")
        // TODO: 跳转到直播间详情页
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension LNNewView: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return self
    }
}
